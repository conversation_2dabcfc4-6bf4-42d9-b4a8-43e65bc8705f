import { Button, Modal, message, Spin, Form, Input, Radio } from 'antd';
import React from 'react';
import connectSession from '@app/utils/connectSession';
import { CommonObject } from '@app/types';
import { releaseListApi } from '@app/api';
import { setMLoading } from '@app/utils/utils';
import { FormComponentProps } from 'antd/es/form';

const { TextArea } = Input;

interface IAIMusicModalProps extends FormComponentProps {
  visible: boolean;
  onClose: () => void;
  onConfirm?: (musicUrl: string, musicName?: string) => void; // ✅ 修改接口，支持传递音乐名称
  config?: CommonObject;
  record?: any; // 接收外部传入的record对象
}

interface IAIMusicModalState {
  currentStep: 'form' | 'audio'; // 当前步骤：表单填写 | 音频播放
  musicUrl: string; // 生成的音乐链接
  musicName: string; // ✅ 生成的音乐名称
  isGeneratingDesc: boolean; // 是否正在生成描述
  isGeneratingMusic: boolean; // 是否正在生成音乐
  formData: any; // 🔧 保存表单数据，确保步骤切换时数据不丢失
}

@connectSession
class AIMusicModal extends React.Component<IAIMusicModalProps, IAIMusicModalState> {
  audioRef: React.RefObject<HTMLAudioElement>;

  constructor(props: IAIMusicModalProps) {
    super(props);
    this.state = {
      currentStep: 'form',
      musicUrl: '',
      musicName: '', // ✅ 初始化音乐名称
      isGeneratingDesc: false,
      isGeneratingMusic: false,
      formData: {}, // 🔧 初始化表单数据存储
    };
    this.audioRef = React.createRef<HTMLAudioElement>();
  }

  componentDidMount() {
    // 🔧 弹窗显示时初始化数据
    if (this.props.visible) {
      this.handleGenerateDescription();
    }
  }

  componentDidUpdate(prevProps: IAIMusicModalProps) {
    // 🔧 弹窗从隐藏变为显示时，重新初始化数据
    if (!prevProps.visible && this.props.visible) {
      this.handleGenerateDescription();
    }
  }

  // ✅ 获取AI描述接口
  handleGenerateDescription = () => {
    const { record } = this.props;
    if (!record || !record.id) {
      message.error('缺少稿件信息');
      return;
    }

    this.setState({ isGeneratingDesc: true });

    // 调用AI提示词生成接口
    releaseListApi.getAiTips({ article_id: record.id })
      .then((res: any) => {
        if (res.code === 0 && res.data) {
          // 获取到描述后，填入表单
          if (this.props.form) {
            this.props.form.setFieldsValue({
              music_style: res.data.music_style || 1, // 填入音乐风格，默认为1（古典）
              music_desc: res.data.music_desc || '' // 填入音乐描述
            });
          }
          this.setState({ isGeneratingDesc: false });
        } else {
          // 接口返回失败，静默处理，不显示错误提示
          console.log('AI提示词生成接口返回失败:', res.msg);
          this.setState({ isGeneratingDesc: false });
        }
      })
      .catch((error) => {
        // 接口调用异常，静默处理，不显示错误提示
        console.error('获取AI描述失败:', error);
        this.setState({ isGeneratingDesc: false });
      });
  };

  // ✅ 重新生成描述
  handleRegenerateDescription = () => {
    this.handleGenerateDescription();
  };

  // ✅ 生成音乐
  handleGenerateMusic = () => {
    const { record } = this.props;
    if (!this.props.form) return;

    if (!record || !record.id) {
      message.error('缺少稿件信息');
      return;
    }

    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        // 🔧 保存表单数据到state中，确保数据不丢失
        this.setState({
          isGeneratingMusic: true,
          formData: values  // 保存当前表单数据
        });

        // 调用AI配乐生成接口
        releaseListApi.getAIMusicDescription({
          article_id: record.id,
          music_style: values.music_style,
          music_desc: values.music_desc
        })
          .then((res: any) => {
            if (res.code === 0 && res.data) {
              this.setState({
                musicUrl: res.data.music_url || '',
                musicName: res.data.music_name || 'AI生成音乐', // ✅ 处理音乐名称
                currentStep: 'audio',
                isGeneratingMusic: false,
                formData: values  // 保存表单数据
              });
            } else {
              // 接口返回失败，静默处理，不显示错误提示
              console.log('AI配乐生成接口返回失败:', res.msg);
              this.setState({ isGeneratingMusic: false });
            }
          })
          .catch((error) => {
            // 接口调用异常，静默处理，不显示错误提示
            console.error('音乐生成失败:', error);
            this.setState({ isGeneratingMusic: false });
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  // ✅ 返回上一步 - 保持表单内容和音乐链接
  handleBackToForm = () => {
    this.setState({
      currentStep: 'form'
      // 🔧 不清空musicUrl，保持音乐链接
    });

    // 🔧 恢复表单数据
    if (this.props.form && this.state.formData) {
      console.log('恢复表单数据:', this.state.formData); // 调试信息
      // 使用setTimeout确保在组件重新渲染后设置表单值
      setTimeout(() => {
        this.props.form.setFieldsValue(this.state.formData);
        console.log('表单数据已恢复'); // 调试信息
      }, 0);
    }
  };

  // ✅ 使用该音乐
  handleConfirmMusic = () => {
    const { musicUrl, musicName } = this.state;
    if (this.props.onConfirm) {
      this.props.onConfirm(musicUrl, musicName); // ✅ 传递音乐链接和名称
    }
    this.handleCloseModal();
  };

  // ✅ 关闭弹窗并清空所有数据
  handleCloseModal = () => {
    // 🔧 重置组件状态
    this.setState({
      currentStep: 'form',
      musicUrl: '',
      musicName: '', // ✅ 清空音乐名称
      isGeneratingDesc: false,
      isGeneratingMusic: false,
      formData: {}, // 🔧 清空保存的表单数据
    });

    // 🔧 清空表单数据
    if (this.props.form) {
      this.props.form.resetFields();
    }

    // 🔧 停止音频播放
    if (this.audioRef.current) {
      this.audioRef.current.pause();
      this.audioRef.current.currentTime = 0;
    }

    // 🔧 调用外部关闭回调
    this.props.onClose();
  };

  // ✅ 渲染表单步骤
  // 🔧 使用Antd Form.create()装饰器，表单数据会自动保持，支持步骤间切换时的数据回显
  renderFormStep = () => {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 }
    };

    return (
      <Form {...formLayout}>
        <Form.Item label="音乐风格" required>
          {getFieldDecorator('music_style', {
            initialValue: 1, // 默认选择古典音乐，对应数字1
            rules: [
              {
                required: true,
                message: '请选择音乐风格',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={1}>古典</Radio>
              <Radio value={2}>流行</Radio>
              <Radio value={3}>电子</Radio>
              <Radio value={4}>乡村</Radio>
              <Radio value={5}>类型不限</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="音乐描述" required>
          {getFieldDecorator('music_desc', {
            rules: [
              {
                required: true,
                message: '请输入音乐描述',
              },
            ],
          })(
            <TextArea
              placeholder="这里是AI为您生成的音乐描述文案，也可以手动修改"
              rows={4}
              maxLength={200}
            />
          )}
          <div style={{ textAlign: 'right', marginTop: 5, color: '#999' }}>
            最多200字
          </div>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 6, span: 16 }}>
          <Button
            type="default"
            onClick={this.handleRegenerateDescription}
            loading={this.state.isGeneratingDesc}
            style={{ marginRight: 10 }}
          >
            重新生成描述
          </Button>
        </Form.Item>
      </Form>
    );
  };

  // ✅ 渲染音频播放步骤
  renderAudioStep = () => {
    const { musicUrl, musicName } = this.state;

    const audioPlayerStyle = {
      borderRadius: 4,
      padding: 15,
      marginBottom: 20,
      border: '1px solid #d9d9d9'
    };

    return (
      <div>
        {/* ✅ 显示音乐名称 */}
        <div style={{ marginBottom: 20 }}>
          <div style={{ marginBottom: 10, fontWeight: 'bold' }}>AI生成：</div>
          <div style={{ fontSize: 14, color: '#333', marginBottom: 15 }}>
            {musicName || '这里是AI生成音乐名称'}
          </div>
          <div style={audioPlayerStyle}>
            <audio
              ref={this.audioRef}
              src={musicUrl}
              style={{ width: '100%' }}
              controls
            />
          </div>
        </div>
      </div>
    );
  };

  // ✅ 渲染底部按钮 - 适配Modal footer
  renderFooterButtons = () => {
    const { currentStep, isGeneratingMusic } = this.state;

    if (currentStep === 'form') {
      return [
        <Button key="cancel" onClick={this.handleCloseModal}>
          取消
        </Button>,
        <Button
          key="generate"
          type="primary"
          onClick={this.handleGenerateMusic}
          loading={isGeneratingMusic}
        >
          生成音乐
        </Button>
      ];
    }

    if (currentStep === 'audio') {
      return [
        <Button key="back" onClick={this.handleBackToForm}>
          上一步
        </Button>,
        <Button
          key="confirm"
          type="primary"
          onClick={this.handleConfirmMusic}
        >
          使用该音乐
        </Button>
      ];
    }

    return null;
  };

  render() {
    const { visible } = this.props;
    const { currentStep, musicUrl, isGeneratingDesc, isGeneratingMusic } = this.state;
    const loading = this.props.config ? this.props.config.mLoading : false;

    const modalContentStyle = {
      padding: 20,
      maxHeight: '60vh',
      overflowY: 'auto' as 'auto'
    };

    return (
      <Modal
        visible={visible}
        title="AI音乐"
        width={600}
        destroyOnClose={true}
        maskClosable={false}
        onCancel={this.handleCloseModal}
        footer={this.renderFooterButtons()}
        bodyStyle={{ padding: 0, position: 'relative' }}
      >
        {(loading || isGeneratingDesc || isGeneratingMusic) && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000
          }}>
            <Spin size="large" />
            <div style={{ marginTop: 16, fontSize: 14, color: '#666' }}>
              {isGeneratingDesc && 'AI正在生成描述中，请稍等...'}
              {isGeneratingMusic && 'AI正在生成音乐中，请稍等...'}
              {loading && '处理中...'}
            </div>
          </div>
        )}

        <div style={modalContentStyle}>
          {currentStep === 'form' && this.renderFormStep()}
          {currentStep === 'audio' && this.renderAudioStep()}
        </div>
      </Modal>
    );
  }
}

export default Form.create<IAIMusicModalProps>()(AIMusicModal);
