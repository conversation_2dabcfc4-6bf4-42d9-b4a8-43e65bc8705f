import { Button, Drawer, message, Spin, Input } from 'antd';
import React from 'react';
import connectSession from '@app/utils/connectSession';
import { CommonObject } from '@app/types';
import { releaseListApi } from '@app/api';
import { setMLoading } from '@app/utils/utils';
import AIMusicModal from './AIMusicModal';
interface IBackgroundMusicModalProps {
  visible: boolean;
  onClose: () => void;
  onOk?: (musicUrl: string) => void;
  config?: CommonObject;
  record?: any; // 添加 record 属性，用于接收当前点击列表行的对象
  onSuccess?: () => void; // 添加成功回调，用于通知外部组件保存成功
  dispatch?: (action: any) => void; // 添加 dispatch 属性，用于派发 action
}

interface IBackgroundMusicModalState {
  music_url: string;
  music_name: string;
  aiMusicModalVisible: boolean;
  loading: boolean;
}

@connectSession
class BackgroundMusicModal extends React.Component<IBackgroundMusicModalProps, IBackgroundMusicModalState> {
  audioRef: React.RefObject<HTMLAudioElement>;

  constructor(props: IBackgroundMusicModalProps) {
    super(props);
    this.state = {
      music_url: props.record?.music_url || '',
      music_name: props.record?.music_name || '',
      aiMusicModalVisible: false,
      loading: false
    };
    this.audioRef = React.createRef<HTMLAudioElement>();
  }

  componentDidUpdate(prevProps: IBackgroundMusicModalProps) {
    // ✅ 弹窗打开时查询背景音乐信息
    if (!prevProps.visible && this.props.visible && this.props.record) {
      this.fetchMusicInfo();
    }

    if (prevProps.record?.music_url !== this.props.record?.music_url && this.props.record?.music_url !== this.state.music_url) {
      this.setState({ music_url: this.props.record?.music_url || '' });
    }
  }

  // ✅ 查询背景音乐信息
  fetchMusicInfo = () => {
    const { record } = this.props;
    if (!record || !record.id) {
      console.log('缺少稿件信息，无法查询背景音乐');
      return;
    }

    this.setState({ loading: true });

    releaseListApi
      .getUgcMusicUrl({
        article_id: record.id // 根据接口文档，只需要 article_id 参数
      })
      .then((res: any) => {
        if (res.code === 0 && res.data) {
          this.setState({
            music_url: res.data.music_url || '',
            music_name: res.data.music_name || '',
            loading: false
          });
        } else {
          console.log('查询背景音乐接口返回失败:', res.msg);
          this.setState({ loading: false });
        }
      })
      .catch((error) => {
        console.error('查询背景音乐失败:', error);
        this.setState({ loading: false });
      });
  };

  handleSaveBackgroundMusic = () => {
    const { record } = this.props;
    if (!record || !record.id) {
      message.error('缺少稿件信息');
      return;
    }

    setMLoading(this, true);
    releaseListApi
      .updateUgcMusicUrl({
        article_id: record.id, // 根据接口文档，参数名应该是 article_id
        music_url: this.state.music_url,
        music_name: this.state.music_name // 音乐名称
      })
      .then((res: any) => {
        if (res.code === 0) {
          message.success('背景音乐设置成功');
          if (this.props.onSuccess) {
            this.props.onSuccess();
          }
          // 保存成功后关闭弹窗
          this.props.onClose();
        } else {
          message.error(res.msg || '保存失败');
        }
        setMLoading(this, false);
      })
      .catch((error) => {
        console.error('保存背景音乐失败:', error);
        message.error('保存失败');
        setMLoading(this, false);
      });
  };

  handleOk = () => {
    if (this.props.onOk) {
      this.props.onOk(this.state.music_url);
    } else {
      this.handleSaveBackgroundMusic();
    }
  };

  handleGenerateAI = () => {
    const { record } = this.props;

    // ✅ 检查record中的pic_array是否为空
    if (!record || !record.pic_array || record.pic_array.length === 0) {
      message.warning('仅带图的短图文可生成音乐');
      return;
    }

    // ✅ 打开AI音乐弹窗
    this.setState({ aiMusicModalVisible: true });
  };

  // ✅ 关闭AI音乐弹窗
  handleCloseAIMusicModal = () => {
    this.setState({ aiMusicModalVisible: false });
  };

  // ✅ 确认使用AI生成的音乐
  handleConfirmAIMusic = (music_url: string, music_name?: string) => {
    this.setState({
      music_url: music_url,
      music_name: music_name || 'AI生成音乐', // ✅ 如果没有传入音乐名称，使用默认名称
      aiMusicModalVisible: false
    });
    message.success('AI音乐已设置');
  };

  handleDeleteMusic = () => {
    this.setState({
      music_url: '',
      music_name: '' // ✅ 同时清空音乐名称
    });
    if (this.audioRef.current) {
      this.audioRef.current.pause();
    }
  };

  // ✅ 处理音乐名称输入变化
  handleMusicNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({ music_name: e.target.value });
  };



  render() {
    const { visible } = this.props;
    const { music_url, music_name, aiMusicModalVisible, loading: fetchLoading } = this.state;
    const loading = this.props.config ? this.props.config.mLoading : false;



    const drawerContentStyle = {
      padding: 20,
      height: 'calc(100% - 108px)',
      overflowY: 'auto' as 'auto'
    };

    const drawerFooterStyle = {
      position: 'absolute' as 'absolute',
      bottom: 0,
      width: '100%',
      padding: '10px 16px',
      textAlign: 'right' as 'right',
      left: 0,
      background: '#fff',
      borderRadius: '0 0 4px 4px'
    };

    const audioPlayerStyle = {
      borderRadius: 4,
      padding: 15,
      marginBottom: 20
    };

    const buttonGroupStyle = {
      display: 'flex',
      justifyContent: 'space-between',
      marginTop: 20
    };
    return (
      <Drawer
        visible={visible}
        title="设置背景音乐"
        className="rox-drawer"
        destroyOnClose={true}
        maskClosable={false}
        width={500}
        onClose={this.props.onClose}
        bodyStyle={{ overflowY: 'hidden' }}
      >
        {(loading || fetchLoading) && <div className="spin-overlay">
          <Spin />
        </div>}
        
        <div style={drawerContentStyle}>
          {/* ✅ 音乐名称输入框 */}
          <div style={{ marginBottom: 20 }}>
            <div style={{ marginBottom: 10 }}>音乐名称：</div>
            <Input
              value={music_name}
              onChange={this.handleMusicNameChange}
              placeholder="请输入音乐名称"
              disabled={loading || fetchLoading}
            />
          </div>

          {/* ✅ 背景音乐播放器区域 */}
          <div style={{ marginBottom: 20 }}>
            <div style={{ marginBottom: 10 }}>背景音乐：</div>
            {music_url ? (
              <div style={audioPlayerStyle}>
                <audio
                  ref={this.audioRef}
                  src={music_url}
                  style={{ width: '100%' }}
                  controls
                />
              </div>
            ) : (
              <div style={{
                ...audioPlayerStyle,
                textAlign: 'center',
                color: '#999',
                background: '#f5f5f5'
              }}>
                暂无，请使用AI生成音乐或手动设置
              </div>
            )}
          </div>

          <div style={buttonGroupStyle}>
            <Button type="primary" onClick={this.handleGenerateAI}>AI生成音乐</Button>
            <Button type="danger" onClick={this.handleDeleteMusic}>删除</Button>
          </div>
        </div>
        
        <div style={drawerFooterStyle}>
          <Button style={{ marginRight: 8 }} onClick={this.props.onClose}>
            取消
          </Button>
          <Button type="primary" onClick={this.handleOk} loading={loading}>
            确定
          </Button>
        </div>

        {/* ✅ AI音乐弹窗组件 */}
        <AIMusicModal
          visible={aiMusicModalVisible}
          onClose={this.handleCloseAIMusicModal}
          onConfirm={this.handleConfirmAIMusic}
          record={this.props.record}
          config={this.props.config}
        />
      </Drawer>
    );
  }
}

export default BackgroundMusicModal;