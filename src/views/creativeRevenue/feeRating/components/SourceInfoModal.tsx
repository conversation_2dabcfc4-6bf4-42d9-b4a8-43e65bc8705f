import React from 'react';
import { Modal, Form } from 'antd';

// 格式化标题显示
const formatTitle = (doc_type: number, list_title: string, html_content: string): string => {
  if (doc_type === 10) {
    // 小视频展示前30字
    return list_title?.slice(0, 30) + (list_title.length > 30 ? '...' : '');
  } else if (doc_type === 12) {
    // 短图文展示描述前30字，纯图显示"图片内容"
    return html_content
      ? list_title?.slice(0, 30) + (list_title?.length > 30 ? '...' : '')
      : '图片内容';
  } else if (doc_type === 13) {
    // 长文章展示完整标题
    return list_title || '';
  }
  return list_title || '';
};

interface SourceInfoModalProps {
  visible: boolean;
  onCancel: () => void;
  sourceInfo: any;
}

const SourceInfoModal: React.FC<SourceInfoModalProps> = ({ visible, onCancel, sourceInfo }) => {
  const { id, doc_type, list_title, url, html_content } = sourceInfo;

  return (
    <Modal
      visible={visible}
      title="源稿信息"
      onCancel={onCancel}
      onOk={onCancel}
      width={400}
      destroyOnClose
      maskClosable={false}
      cancelButtonProps={{ style: { display: 'none' } }}
    >
      <div>
        <div>
          <span>潮新闻ID：</span>
          <span>{id}</span>
        </div>
        <div>
          <span>稿件类型：</span>
          <span>
            {doc_type === 10
              ? '短视频'
              : doc_type === 12
              ? '短图文'
              : doc_type === 13
              ? '长图文'
              : doc_type}
          </span>
        </div>
        <div>
          <span>稿件链接：</span>
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            onMouseOver={(e) => (e.currentTarget.style.textDecoration = 'underline')}
            onMouseOut={(e) => (e.currentTarget.style.textDecoration = 'none')}
          >
            {formatTitle(doc_type, list_title, html_content)}
          </a>
        </div>
      </div>
    </Modal>
  );
};

export default SourceInfoModal;
